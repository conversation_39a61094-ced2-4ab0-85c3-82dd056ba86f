/************************************************
 * Copyright(c) 2020 uni-ubi
 * 
 * Project:    SACApp
 * FileName:   TrafficControlImpl.cpp
 * Author:     yuanshen
 * Email:      <EMAIL>
 * Version:    V1.0.0
 * Date:       2022/4/7 10:35
 * Description: 
 * Others:
 *************************************************/

#include "UBase/Error.h"
#include "ProtocolKey.h"
#include "Utils/String.h"
#include "Object/Factory.h"
#include "MagixBox/System.h"
#include <fstream>
#include "System/AlarmCtrl.h"
#include "Config/AppConfig.h"
#include "UBase/File/File.h"
#include "System/SystemCard.h"
#include "System/SwitchCtrl.h"
#include "TrafficControlImpl.h"
#include "Stream/AudioStream.h"
#include "System/WiegandCtrl.h"
#include "Device/DeviceManager.h"
#include "AlgReactor/AlgManager.h"
#include "AppEvent/StatusManager.h"
#include "Resource/ResourceManager.h"
#include "CustomPlugin/customClient.h"
#include "Private/PrivateAccessCtrl.h"
#include "Traffic/System/SystemControl.h"
#include "Traffic/Data/SystemApiHandler.h"
#include "Device/AlgReactor/IdentifyCommon.h"
#include "Traffic/Data/DataCapacity.h"
#include "System/Capacity.h"
#include "Config/ConfigLoader.h"
#include "Resource/LangResource.h"
#include "BuildConfig.h"

using namespace Uface::Traffic;

namespace Uface {
namespace Application {
typedef struct {
    const char* key;
    const char* url;
    const char* icon;
} WebPageDefine;

std::string TrafficControlImpl::mLang;
std::string TrafficControlImpl::mDefLang = "";
std::string TrafficControlImpl::mDefTimezone = "";

TrafficControlImpl::TrafficControlImpl() {
    mDefTimezone = PRODUCT_TIMEZONE;
    mDefLang = PRODUCT_LANG;

    //如果存在定制文件 CUSTOM_FILE,则读取定制文件中的PPRODUCT_LANG PRODUCT_TIMEZONE 字段
    while (std::string(CUSTOM_FILE) != "")
    {
        // 读取文件内容
        std::ifstream file(CUSTOM_FILE);
        if (!file.is_open()) {
            errorf("无法打开输入文件 %s\n", CUSTOM_FILE);
            break;
        }
        
        std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
        file.close();
        infof("成功读取文件，内容长度: %zu\n", content.length());
        
        // 解析 JSON 数据
        Json::Value root;
        Json::Reader reader;
        if (!reader.parse(content, root)) {
            errorf("JSON解析失败: %s\n", reader.getFormattedErrorMessages().c_str());
            break;
        }

        // 提取指定字段
        if (root.isMember("PRODUCT_TIMEZONE") && root["PRODUCT_TIMEZONE"].isString()) {
            mDefTimezone = root["PRODUCT_TIMEZONE"].asString();
            infof("成功提取字段 PRODUCT_TIMEZONE: %s\n", mDefTimezone.c_str());
        }

        if (root.isMember("PPRODUCT_LANG") && root["PPRODUCT_LANG"].isString()) {
            mDefLang = root["PPRODUCT_LANG"].asString();
            infof("成功提取字段 PPRODUCT_LANG: %s\n", mDefLang.c_str());
        }
        
        break;
    }

    mLang = Resource::ILangResource::instance()->getLanguage();
    infof("mm@ mLang:%s, length:%zu\n", mLang.c_str(), mLang.size());
    if (mLang.size() == 0)
    {
        infof("mm@ mLang is empty, try to load from systemConfig\n");
        Json::Value data = Json::nullValue;
        if (Config::CConfigLoader::instance()->loadConfig("/userdata/config/os/systemConfig", data)) {
            mLang = data["language"].asString();
            infof("mm@ cfg mLang:%s\n", mLang.c_str());
        }
        else
        {
            if (mDefLang != "") {
                mLang = mDefLang;
                infof("mm@ mLang use mDefLang :%s\n", mLang.c_str());
                // todo  当不存在配置时，是否以宏定义或者定制文件中的默认语言，是否需要联动修改 product 下的 systemConfig
                Json::Value deviceInfo {Json::nullValue};
                MagixBox::ISystem::instance()->getDeviceInfo("IDeviceManager", deviceInfo);
                infof("mm@ deviceInfo %s\n", deviceInfo.toStyledString().c_str());
                std::string configPath = std::string("/product/") + deviceInfo["deviceModel"].asString() + "/os/systemConfig";
                infof("mm@ configPath %s\n", configPath.c_str());
                replaceJsonValue(configPath, "language", mLang);
            }
            else
            {
                mLang = "zh";
                infof("mm@ mLang use zh :%s\n", mLang.c_str());
            }
        }
    }
}

TrafficControlImpl::~TrafficControlImpl() {
}

void TrafficControlImpl::destroy() {
    delete this;
}

void TrafficControlImpl::replaceJsonValue(const std::string& filePath, const std::string& key, const std::string& value) {
    infof("replaceJsonValue: 开始处理文件 %s key %s value %s \n", filePath.c_str(), key.c_str(), value.c_str());
    
    // 读取文件内容
    std::ifstream file(filePath);
    if (!file.is_open()) {
        errorf("replaceJsonValue: 无法打开输入文件 %s\n", filePath.c_str());
        return;
    }
    
    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();
    infof("replaceJsonValue: 成功读取文件，内容长度: %zu\n", content.length());
    
    // 解析 JSON 数据
    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(content, root)) {
        errorf("replaceJsonValue: JSON解析失败: %s\n", reader.getFormattedErrorMessages().c_str());
        return;
    }
    infof("replaceJsonValue: JSON解析成功\n");
    
    // 判断是否存在 key
    if (root.isMember(key)) {
        root[key] = value;
    } else {
        errorf("replaceJsonValue: 未找到键 %s\n", key.c_str());
        return;
    }
    
    // 写回文件
    const std::string outputPath = filePath;
    infof("replaceJsonValue: 准备写入文件 %s\n", outputPath.c_str());
    
    std::ofstream outFile(outputPath);
    if (!outFile.is_open()) {
        errorf("replaceJsonValue: 无法打开输出文件 %s\n", outputPath.c_str());
        return;
    }
    
    Json::StyledWriter writer;
    std::string jsonOutput = writer.write(root);
    outFile << jsonOutput;
    outFile.close();
    
    if (outFile.fail()) {
        errorf("replaceJsonValue: 写入文件失败 %s\n", outputPath.c_str());
    } else {
        infof("replaceJsonValue: 成功写入文件 %s，内容长度: %zu\n", outputPath.c_str(), jsonOutput.length());
    }
}

bool TrafficControlImpl::restoreConfig(const char* clientId, const Json::Value& except) {

    TrafficControlPtr ctrlPtr = Object::IFactory::instance<ITrafficControl>("custom.trafficControl");
    if (ctrlPtr!=osNullptr && CustomClient::instance()->hasFunction("ITrafficControl", "restoreConfig")) {
        return ctrlPtr->restoreConfig(clientId, except);
    }

    return AppConfig::instance()->recover(clientId,Json::nullValue);
}

bool TrafficControlImpl::convertCardNo(const char *src, std::string &dst,CardType type, const Json::Value &attr) {
    TrafficControlPtr ctrlPtr = Object::IFactory::instance<ITrafficControl>("custom.trafficControl");
    if (ctrlPtr!=osNullptr && CustomClient::instance()->hasFunction("ITrafficControl", "convertCardNo")) {
        return ctrlPtr->convertCardNo(src, dst, type, attr);
    }

    bool ret = false;
    switch (type) {
        case systemCard:
            ret = getSystemCardNo(src,dst);
            break;
        case inputCard:
            ret = getExternalCardNo(src,dst,attr);
            break;
        default:
            break;
    }
    tracef("convert card no type:%d, src:%s, dst:%s, attr:%s\n", type, src, dst.c_str(), attr.toStyledString().c_str());
    return ret;
}

bool TrafficControlImpl::getCompatibleParams(Json::Value &params) {
    TrafficControlPtr ctrlPtr = Object::IFactory::instance<ITrafficControl>("custom.trafficControl");
    if (ctrlPtr!=osNullptr && CustomClient::instance()->hasFunction("ITrafficControl", "getCompatibleParams")) {
        return ctrlPtr->getCompatibleParams(params);
    }

    Json::Value userParam = AppConfig::instance()->getAppConfig("user_param");
    if (userParam.isMember(PASSWORD)) {
        params[PASSWORD] = userParam[PASSWORD];
    }

    if (UBase::File::exist(OLD_AIOT_CONFIG_PATH)) {
        getAiotConfig(OLD_AIOT_CONFIG_PATH, params);
    }

    //自定义对称密钥和偏移量
    params["platSafe"]["platIV"] = "0123456789012345";   //16字节
    params["platSafe"]["platKey"] = "012345678901234567890123";   //24字节
    return true;
}

bool TrafficControlImpl::getTrafficDataCaps(Json::Value &appCaps) {
    TrafficControlPtr appCtrl = Object::IFactory::instance<ITrafficControl>("custom.trafficControl");
    if (appCtrl!=osNullptr && CustomClient::instance()->hasFunction("ITrafficControl", "getTrafficDataCaps")) {
        return appCtrl->getTrafficDataCaps(appCaps);
    }

    appCaps["trafficPerson"] = AppConfig::instance()->getDefaultConfig("trafficPerson");
    appCaps["trafficRule"] = AppConfig::instance()->getDefaultConfig("trafficRule");
    appCaps["identifyRecord"] = AppConfig::instance()->getDefaultConfig("identifyRecord");
    /**sdk会判断是否有该能力*/
    appCaps["face"] = AppConfig::instance()->getDefaultConfig("face");
    appCaps["finger"] = AppConfig::instance()->getDefaultConfig("finger");
    
    /**追加Web上活体检测的配置能力*/
    appCaps["identify"]["face"][0]["aliveEnable"] = true;

    /*不支持身份证能力 - 关闭1:1阈值配置能力*/
    if (!checkIdCardCap()) {
        appCaps["identify"]["face"][0]["compareThreshold"]["1_1"] = Json::nullValue;
    }

    //熄屏时间范围调整到10-300s
    appCaps["mainDisplay"]["screenSaverTime"]["range"][0] = 10;
    appCaps["mainDisplay"]["screenSaverTime"]["range"][1] = 300;
    infof("getTrafficDataCaps: %s\n", appCaps.toStyledString().c_str());
    return true;
}

void TrafficControlImpl::translatePlatformAccess(std::string filePath) {
    infof("translatePlatformAccess: 开始处理文件 %s\n", filePath.c_str());
    
    // 读取文件内容
    std::ifstream file(filePath);
    if (!file.is_open()) {
        errorf("translatePlatformAccess: 无法打开输入文件 %s\n", filePath.c_str());
        return;
    }
    
    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();
    infof("translatePlatformAccess: 成功读取文件，内容长度: %zu\n", content.length());
    
    // 解析 JSON 数据
    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(content, root)) {
        errorf("translatePlatformAccess: JSON解析失败: %s\n", reader.getFormattedErrorMessages().c_str());
        return;
    }
    infof("translatePlatformAccess: JSON解析成功\n");

    // 获取coiParam和pubsecParam属性
    Json::Value& coiParam = root["properties"]["layout"]["properties"]["collapse"]["properties"]["coiParam"];
    Json::Value& platformAccess = root["properties"]["layout"]["properties"]["collapse"]["properties"]["coiParam"]["properties"]["platformAccess"]["properties"];
    Json::Value& pubsecParam = root["properties"]["layout"]["properties"]["collapse"]["properties"]["pubsecParam"];
    Json::Value& videoLibrary = root["properties"]["layout"]["properties"]["collapse"]["properties"]["pubsecParam"]["properties"]["videoLibrary"]["properties"];
    
    // 根据当前语言翻译字段
    if (mLang == "zh") {
        // COI配置翻译
        coiParam["x-component-props"]["tab"] = "COI配置";
        platformAccess["platformEnable"]["title"] = "启用";
        platformAccess["platformBaseurl"]["title"] = "平台地址";
        platformAccess["platformBaseurl"]["x-validator"][0]["message"] = "请输入平台地址";
        platformAccess["deviceId"]["title"] = "入网ID";
        
        // 视图库配置翻译
        pubsecParam["x-component-props"]["tab"] = "视图库配置";
        videoLibrary["videoLibrary"]["title"] = "注册视图库";
        videoLibrary["videoLibrary"]["enum"][0]["label"] = "视图库";
        videoLibrary["enable"]["title"] = "启用";
        videoLibrary["deviceId"]["title"] = "入网ID";
        videoLibrary["platformAddress"]["title"] = "平台地址";
        videoLibrary["videoLibPort"]["title"] = "视图库端口";
        videoLibrary["username"]["title"] = "注册用户名";
        videoLibrary["password"]["title"] = "注册密码";
        videoLibrary["heartbeatInterval"]["title"] = "心跳时间";
        videoLibrary["heartbeatInterval"]["x-validator"][0]["message"] = "请输入不小于30且不大于300的数字";
        videoLibrary["installAddress"]["title"] = "安装地址";
        videoLibrary["adminArea"]["title"] = "行政区域划分";
        videoLibrary["sendMode"]["title"] = "发送方式";
        videoLibrary["sendMode"]["enum"][0]["label"] = "图片集合";
        videoLibrary["sendMode"]["enum"][1]["label"] = "对象列表";
        videoLibrary["imagePriority"]["title"] = "图传优先";
        videoLibrary["uploadType"]["title"] = "上传类型";
        videoLibrary["autoSync"]["title"] = "自动校时";
        videoLibrary["syncInterval"]["title"] = "校时时间间隔";
    } else if (mLang == "pt") {
        // COI配置翻译
        coiParam["x-component-props"]["tab"] = "Configuração COI";
        platformAccess["platformEnable"]["title"] = "Ativar";
        platformAccess["platformBaseurl"]["title"] = "Endereço do Servidor";
        platformAccess["platformBaseurl"]["x-validator"][0]["message"] = "Por favor, insira um endereço de servidor válido";
        platformAccess["deviceId"]["title"] = "ID do Dispositivo";
        
        // 视图库配置翻译
        pubsecParam["x-component-props"]["tab"] = "Configuração Pubsec";
        videoLibrary["videoLibrary"]["title"] = "Registrar Pubsec";
        videoLibrary["videoLibrary"]["enum"][0]["label"] = "Pubsec";
        videoLibrary["enable"]["title"] = "Ativar";
        videoLibrary["deviceId"]["title"] = "ID do Dispositivo";
        videoLibrary["platformAddress"]["title"] = "Endereço do Servidor";
        videoLibrary["videoLibPort"]["title"] = "Porta do Servidor";
        videoLibrary["username"]["title"] = "Nome de Usuário de Registro";
        videoLibrary["password"]["title"] = "Senha de Registro";
        videoLibrary["heartbeatInterval"]["title"] = "Tempo de Heartbeat";
        videoLibrary["heartbeatInterval"]["x-validator"][0]["message"] = "Por favor, insira um número não menor que 30 e não maior que 300";
        videoLibrary["installAddress"]["title"] = "Endereço de Instalação";
        videoLibrary["adminArea"]["title"] = "Divisão da Área Administrativa";
        videoLibrary["sendMode"]["title"] = "Modo de Envio";
        videoLibrary["sendMode"]["enum"][0]["label"] = "Coleção de Imagens";
        videoLibrary["sendMode"]["enum"][1]["label"] = "Lista de Objetos";
        videoLibrary["imagePriority"]["title"] = "Prioridade de Transmissão de Imagem";
        videoLibrary["uploadType"]["title"] = "Tipo de Upload";
        videoLibrary["autoSync"]["title"] = "Sincronização Automática de Tempo";
        videoLibrary["syncInterval"]["title"] = "Intervalo de Sincronização de Tempo";
    } else if (mLang == "en") {
        // COI配置翻译
        coiParam["x-component-props"]["tab"] = "COI Configuration";
        platformAccess["platformEnable"]["title"] = "On";
        platformAccess["platformBaseurl"]["title"] = "Server Address";
        platformAccess["platformBaseurl"]["x-validator"][0]["message"] = "Please enter a valid Server Address";
        platformAccess["deviceId"]["title"] = "Device ID";
        
        // 视图库配置翻译
        pubsecParam["x-component-props"]["tab"] = "Pubsec Configuration";
        videoLibrary["videoLibrary"]["title"] = "Register Pubsec";
        videoLibrary["videoLibrary"]["enum"][0]["label"] = "Pubsec";
        videoLibrary["enable"]["title"] = "On";
        videoLibrary["deviceId"]["title"] = "Device ID";
        videoLibrary["platformAddress"]["title"] = "Server Address";
        videoLibrary["videoLibPort"]["title"] = "Server Port";
        videoLibrary["username"]["title"] = "Registration Username";
        videoLibrary["password"]["title"] = "Registration Password";
        videoLibrary["heartbeatInterval"]["title"] = "Heartbeat Time";
        videoLibrary["heartbeatInterval"]["x-validator"][0]["message"] = "Please enter a number not less than 30 and not greater than 300";
        videoLibrary["installAddress"]["title"] = "Installation Address";
        videoLibrary["adminArea"]["title"] = "Administrative Area Division";
        videoLibrary["sendMode"]["title"] = "Send Mode";
        videoLibrary["sendMode"]["enum"][0]["label"] = "Image Collection";
        videoLibrary["sendMode"]["enum"][1]["label"] = "Object List";
        videoLibrary["imagePriority"]["title"] = "Image Transmission Priority";
        videoLibrary["uploadType"]["title"] = "Upload Type";
        videoLibrary["autoSync"]["title"] = "Auto Time Sync";
        videoLibrary["syncInterval"]["title"] = "Time Sync Interval";
    }

    // 写回文件
    const std::string outputPath = "/application/neutron/res/web/platformAccess.json";
    infof("translatePlatformAccess: 准备写入文件 %s\n", outputPath.c_str());
    
    std::ofstream outFile(outputPath);
    if (!outFile.is_open()) {
        errorf("translatePlatformAccess: 无法打开输出文件 %s\n", outputPath.c_str());
        return;
    }
    
    Json::StyledWriter writer;
    std::string jsonOutput = writer.write(root);
    outFile << jsonOutput;
    outFile.close();
    
    if (outFile.fail()) {
        errorf("translatePlatformAccess: 写入文件失败 %s\n", outputPath.c_str());
    } else {
        infof("translatePlatformAccess: 成功写入文件 %s，内容长度: %zu\n", outputPath.c_str(), jsonOutput.length());
    }
}

void TrafficControlImpl::createCustomWebJson(const std::string& filePath, Json::Value& params) {
    infof("createCustomWebJson: 开始处理文件 %s\n", filePath.c_str());
    
    // 读取文件内容
    std::ifstream file(filePath);
    if (!file.is_open()) {
        errorf("createCustomWebJson: 无法打开输入文件 %s\n", filePath.c_str());
        return;
    }
    
    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();
    infof("createCustomWebJson: 成功读取文件，内容长度: %zu\n", content.length());
    
    // 解析 JSON 数据
    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(content, root)) {
        errorf("createCustomWebJson: JSON解析失败: %s\n", reader.getFormattedErrorMessages().c_str());
        return;
    }
    infof("createCustomWebJson: JSON解析成功\n");
    
    // 根据参数配置门磁配置和熄屏配置的启用状态
    bool sensParam = params.isMember("sensParam") ? params["sensParam"].asBool() : false;
    bool screenParam = params.isMember("screenParam") ? params["screenParam"].asBool() : false;
    infof("mm@ createCustomWebJson: sensParam:%d, screenParam:%d\n", sensParam, screenParam);
    
    // 如果不启用门磁配置，则移除相关配置项
    if (!sensParam) {
        if (root["properties"]["layout"]["properties"]["collapse"]["properties"].isMember("sensParam")) {
            Json::Value removed;
            root["properties"]["layout"]["properties"]["collapse"]["properties"].removeMember("sensParam", &removed);
            infof("createCustomWebJson: 已移除sensParam配置项\n");
        }
    }
    
    // 如果不启用熄屏配置，则移除相关配置项
    if (!screenParam) {
        if (root["properties"]["layout"]["properties"]["collapse"]["properties"].isMember("screenParam")) {
            Json::Value removed;
            root["properties"]["layout"]["properties"]["collapse"]["properties"].removeMember("screenParam", &removed);
            infof("createCustomWebJson: 已移除screenParam配置项\n");
        }
    }
    
    // 写回文件
    const std::string outputPath = "/application/neutron/res/web/custom.json";
    infof("createCustomWebJson: 准备写入文件 %s\n", outputPath.c_str());
    
    std::ofstream outFile(outputPath);
    if (!outFile.is_open()) {
        errorf("createCustomWebJson: 无法打开输出文件 %s\n", outputPath.c_str());
        return;
    }
    
    Json::StyledWriter writer;
    std::string jsonOutput = writer.write(root);
    outFile << jsonOutput;
    outFile.close();
    
    if (outFile.fail()) {
        errorf("createCustomWebJson: 写入文件失败 %s\n", outputPath.c_str());
    } else {
        infof("createCustomWebJson: 成功写入文件 %s，内容长度: %zu\n", outputPath.c_str(), jsonOutput.length());
    }
}

void TrafficControlImpl::createAccessWebJson(const std::string& filePath, Json::Value& params) {
    infof("createAccessWebJson: 开始处理文件 %s\n", filePath.c_str());
    
    // 读取文件内容
    std::ifstream file(filePath);
    if (!file.is_open()) {
        errorf("createAccessWebJson: 无法打开输入文件 %s\n", filePath.c_str());
        return;
    }
    
    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();
    infof("createAccessWebJson: 成功读取文件，内容长度: %zu\n", content.length());
    
    // 解析 JSON 数据
    Json::Value root;
    Json::Reader reader;
    if (!reader.parse(content, root)) {
        errorf("createAccessWebJson: JSON解析失败: %s\n", reader.getFormattedErrorMessages().c_str());
        return;
    }
    infof("createAccessWebJson: JSON解析成功\n");
    
    // 根据参数配置COI配置和视图库配置的启用状态
    bool coiParam = params.isMember("coi") ? params["coi"].asBool() : false;
    bool pubsecParam = params.isMember("pubsec") ? params["pubsec"].asBool() : false;

    infof("mm@ createAccessWebJson: coiParam:%d, pubsecParam:%d\n", coiParam, pubsecParam);
    
    // 如果不启用COI配置，则移除相关配置项
    if (!coiParam) {
        if (root["properties"]["layout"]["properties"]["collapse"]["properties"].isMember("coiParam")) {
            Json::Value removed;
            root["properties"]["layout"]["properties"]["collapse"]["properties"].removeMember("coiParam", &removed);
            infof("createAccessWebJson: 已移除coiParam配置项\n");
        }
    }
    
    // 如果不启用视图库配置，则移除相关配置项
    if (!pubsecParam) {
        if (root["properties"]["layout"]["properties"]["collapse"]["properties"].isMember("pubsecParam")) {
            Json::Value removed;
            root["properties"]["layout"]["properties"]["collapse"]["properties"].removeMember("pubsecParam", &removed);
            infof("createAccessWebJson: 已移除pubsecParam配置项\n");
        }
    }
    
    // 写回文件
    const std::string outputPath = "/application/neutron/res/web/platformAccess.json";
    infof("createAccessWebJson: 准备写入文件 %s\n", outputPath.c_str());
    
    std::ofstream outFile(outputPath);
    if (!outFile.is_open()) {
        errorf("createAccessWebJson: 无法打开输出文件 %s\n", outputPath.c_str());
        return;
    }
    
    Json::StyledWriter writer;
    std::string jsonOutput = writer.write(root);
    outFile << jsonOutput;
    outFile.close();
    
    if (outFile.fail()) {
        errorf("createAccessWebJson: 写入文件失败 %s\n", outputPath.c_str());
    } else {
        infof("createAccessWebJson: 成功写入文件 %s，内容长度: %zu\n", outputPath.c_str(), jsonOutput.length());
    }
}

bool TrafficControlImpl::getTrafficAccessCaps(std::string &ver, Json::Value &caps) {

    TrafficControlPtr ctrlPtr = Object::IFactory::instance<ITrafficControl>("custom.trafficControl");
    if (ctrlPtr!=osNullptr && CustomClient::instance()->hasFunction("ITrafficControl", "getTrafficAccessCaps")) {
        return ctrlPtr->getTrafficAccessCaps(ver, caps);
    }
    /**ver代表aiot协议版本号*/
    ver = "1.0";
    Json::Value webApp = Json::nullValue;
    webApp["enable"] = true;
//    std::vector<std::string> items = {"personManage", "recgRecord", "w_io", "w_appSet"};
    WebPageDefine webDef[4] = {
            {"personManage","/staff","PersonFilled"},
            {"recgRecord","/record","ViewFilled"},
            {"w_io","/io","AchievementsFilled"},
            {"w_appSet","/application","CommonFilled"},
    };
    uint32_t size = sizeof(webDef)/ sizeof(WebPageDefine);
    for (uint32_t index = 0; index < size; index++) {

        Json::Value item = Json::nullValue;
//        item["key"] = items[index];
        item["key"]  = webDef[index].key;
        item["url"]  = webDef[index].url;
        item["icon"] = webDef[index].icon;
        item["version"] = "V1";
        webApp["page"].append(item);
    }

    //网络中添加自定义配置界面, value需要与界面json文件名以及翻译key对应
    webApp["custom"]["network"]["deviceAccess"] = "platformAccess";
    Json::Value supWeb = Json::nullValue;
    supWeb["coi"] = true;
    supWeb["pubsec"] = IS_CUSTOM_MODE("pubsec") ? true : false;
    translatePlatformAccess("/application/neutron/res/web/platformAccessBase.json");
    createAccessWebJson("/application/neutron/res/web/platformAccess.json", supWeb);

    //重新定义Http超时时间
    webApp["timeout"] = 10;
    webApp["exclude"]["systemSet"]["declare"] = true;//mm@ 20230420 去除系统设置法律法规界面

    if ((IS_CUSTOM_MODE("customweb")))
    {
        Json::Value supWeb = Json::nullValue;
        supWeb["sensParam"] = IS_CUSTOM_WEB_MODE(CUSTOM_WEB_SENS);
        supWeb["screenParam"] = IS_CUSTOM_WEB_MODE(CUSTOM_WEB_SCREEN);
        createCustomWebJson("/application/neutron/res/web/customBase.json", supWeb);

        //自定义配置界面
        Json::Value custom =Json::nullValue;
        custom["custom"]= true;
        custom["title"]="其他配置";
        custom["getRequest"]="/api/custom/getCustomConfig";
        custom["setRequest"]="/api/custom/setCustomConfig",
        custom["key"]="custom";     // custom.json
        custom["url"]="/custom";
        custom["icon"]="SettOutlined";
        custom["version"]="V1";
        webApp["page"].append(custom);
    }

    //重新定义Http Server头
    webApp["serverHeader"] = "";

    caps["webApp"] = webApp;
    caps["streamApp"]["rtmp"] = true;

    /**检查是否oem设备*/
    //if (IDeviceManager::instance()->checkOEM()) 
    
    // 科达定制要求去除公司标识，都强制设置成中性设备
    {
        caps["oem"] = true;
        /**去除uni-ubi默认证书*/
        Json::Value& capsConfig = caps["config"];
        capsConfig["security"] = Json::nullValue;

        //关闭AIOT能力
        caps["aiot"]["enable"] = false;
        caps["aiot"]["ctrl"] = false;

    }

    return true;
}

static std::map<std::string, std::string> langMap {
    {"zh","简体中文"},
    {"en", "English"},
    {"pt", "português"}
    // {"ru", "Русский"},
    // {"fa", "پارسی"},
    // {"ko", "국문"},
    // {"kz", "Қазақша"},
    // {"es", "Español"},
    // {"th", "ภาษาไทย"},
    // {"ja", "日本語"},
    // {"cs", "Čeština"},
    // {"tr", "Türkçe"},
    // {"pt", "português"},
    // {"zh_TW", "中文繁體"},
    // {"vn", "Việt nam"},
    // {"ar", "عرب"},
    // {"ind", "Indonesia"},
    // {"fr", "français"},
    // {"de", "Deutsch"}
};

std::vector<std::string> orderedTimezones = {
    "Etc/GMT+12",
    "Etc/GMT+11",
    "Pacific/Honolulu",
    "America/Anchorage",
    "America/Los_Angeles",
    "America/Denver",
    "America/Chicago",
    "America/New_York",
    "America/Halifax",
    "America/Sao_Paulo",
    "Etc/GMT+2",
    "Atlantic/Cape_Verde",
    "Europe/London",
    "Europe/Berlin",
    "Europe/Kiev",
    "Europe/Minsk",
    "Asia/Dubai",
    "Asia/Karachi",
    "Asia/Dhaka",
    "Asia/Bangkok",
    "Asia/Shanghai",
    "Asia/Tokyo",
    "Australia/Sydney",
    "Pacific/Guadalcanal",
    "Pacific/Auckland",
    "Pacific/Tongatapu"
};

static std::map<std::string, std::map<std::string, std::string>> timezoneTranslations {
    {"Etc/GMT+12", {{
        {"zh", "(UTC-12:00) 国际日期变更线西"},
        {"en", "(UTC-12:00) International Date Line West"},
        {"pt", "(UTC-12:00) Oeste da Linha Internacional de Data"}
    }}},
    {"Etc/GMT+11", {{
        {"zh", "(UTC-11:00) 协调世界时-11"},
        {"en", "(UTC-11:00) Coordinated Universal Time-11"},
        {"pt", "(UTC-11:00) Hora Universal Coordenada-11"}
    }}},
    {"Pacific/Honolulu", {{
        {"zh", "(UTC-10:00) 夏威夷"},
        {"en", "(UTC-10:00) Hawaii"},
        {"pt", "(UTC-10:00) Havai"}
    }}},
    {"America/Anchorage", {{
        {"zh", "(UTC-09:00) 安克雷奇"},
        {"en", "(UTC-09:00) Anchorage"},
        {"pt", "(UTC-09:00) Anchorage"}
    }}},
    {"America/Los_Angeles", {{
        {"zh", "(UTC-08:00) 太平洋时间(美国和加拿大)"},
        {"en", "(UTC-08:00) Pacific Time (US & Canada)"},
        {"pt", "(UTC-08:00) Hora do Pacífico (E.U.A. e Canadá)"}
    }}},
    {"America/Denver", {{
        {"zh", "(UTC-07:00) 山地时间(美国和加拿大)"},
        {"en", "(UTC-07:00) Mountain Time (US & Canada)"},
        {"pt", "(UTC-07:00) Hora das Regiões Montanhosas (E.U.A. e Canadá)"}
    }}},
    {"America/Chicago", {{
        {"zh", "(UTC-06:00) 中部时间(美国和加拿大)"},
        {"en", "(UTC-06:00) Central Time (US & Canada)"},
        {"pt", "(UTC-06:00) Hora Central (E.U.A. & Canadá)"}
    }}},
    {"America/New_York", {{
        {"zh", "(UTC-05:00) 东部时间(美国和加拿大)"},
        {"en", "(UTC-05:00) Eastern Time (US & Canada)"},
        {"pt", "(UTC-05:00) Hora do Leste (E.U.A. e Canadá)"}
    }}},
    {"America/Halifax", {{
        {"zh", "(UTC-04:00) 大西洋时间(加拿大)"},
        {"en", "(UTC-04:00) Atlantic Time (Canada)"},
        {"pt", "(UTC-04:00) Hora do Atlântico (Canadá)"}
    }}},
    {"America/Sao_Paulo", {{
        {"zh", "(UTC-03:00) 巴西利亚"},
        {"en", "(UTC-03:00) Brasilia"},
        {"pt", "(UTC-03:00) Brasília"}
    }}},
    {"Etc/GMT+2", {{
        {"zh", "(UTC-02:00) 协调世界时-2"},
        {"en", "(UTC-02:00) Coordinated Universal Time-2"},
        {"pt", "(UTC-02:00) Hora Universal Coordenada-2"}
    }}},
    {"Atlantic/Cape_Verde", {{
        {"zh", "(UTC-01:00) 佛得角群岛"},
        {"en", "(UTC-01:00) Cape Verde Islands"},
        {"pt", "(UTC-01:00) Ilhas de Capo Verde"}
    }}},
    {"Europe/London", {{
        {"zh", "(UTC+00:00) 都柏林,爱丁堡,里斯本,伦敦"},
        {"en", "(UTC+00:00) Dublin, Edinburgh, Lisbon, London"},
        {"pt", "(UTC+00:00) Dublin, Edimburgo, Lisboa, Londres"}
    }}},
    {"Europe/Berlin", {{
        {"zh", "(UTC+01:00) 阿姆斯特丹,柏林,伯尔尼,罗马,斯德哥尔摩,维也纳"},
        {"en", "(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna"},
        {"pt", "(UTC+01:00) Amesterdão, Berlim, Berna, Roma, Estocolmo, Viena"}
    }}},
    {"Europe/Kiev", {{
        {"zh", "(UTC+02:00) 赫尔辛基,基辅,里加,索非亚,塔林,维尔纽斯"},
        {"en", "(UTC+02:00) Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius"},
        {"pt", "(UTC+02:00) Helsinque, Kiev, Riga, Sófia, Tallinn, Vilnius"}
    }}},
    {"Europe/Minsk", {{
        {"zh", "(UTC+03:00) 明斯克"},
        {"en", "(UTC+03:00) Minsk"},
        {"pt", "(UTC+03:00) Minsk"}
    }}},
    {"Asia/Dubai", {{
        {"zh", "(UTC+04:00) 阿布扎比,马斯喀特"},
        {"en", "(UTC+04:00) Abu Dhabi, Muscat"},
        {"pt", "(UTC+04:00) Abu Dhabi, Muscat"}
    }}},
    {"Asia/Karachi", {{
        {"zh", "(UTC+05:00) 伊斯兰堡,卡拉奇"},
        {"en", "(UTC+05:00) Islamabad, Karachi"},
        {"pt", "(UTC+05:00) Islamabade, Carachi"}
    }}},
    {"Asia/Dhaka", {{
        {"zh", "(UTC+06:00) 达卡"},
        {"en", "(UTC+06:00) Dhaka"},
        {"pt", "(UTC+06:00) Daka"}
    }}},
    {"Asia/Bangkok", {{
        {"zh", "(UTC+07:00) 曼谷,河内,雅加达"},
        {"en", "(UTC+07:00) Bangkok, Hanoi, Jakarta"},
        {"pt", "(UTC+07:00) Banguecoque, Hanói, Jacarta"}
    }}},
    {"Asia/Shanghai", {{
        {"zh", "(UTC+08:00) 北京,重庆,香港,乌鲁木齐"},
        {"en", "(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi"},
        {"pt", "(UTC+08:00) Pequim, Chongqing, RAE de Hong Kong, Urumqi"}
    }}},
    {"Asia/Tokyo", {{
        {"zh", "(UTC+09:00) 大阪,札幌,东京"},
        {"en", "(UTC+09:00) Osaka, Sapporo, Tokyo"},
        {"pt", "(UTC+09:00) Osaca, Sapporo, Tóquio"}
    }}},
    {"Australia/Sydney", {{
        {"zh", "(UTC+10:00) 堪培拉,墨尔本,悉尼"},
        {"en", "(UTC+10:00) Canberra, Melbourne, Sydney"},
        {"pt", "(UTC+10:00) Camberra, Melbourne, Sidnei"}
    }}},
    {"Pacific/Guadalcanal", {{
        {"zh", "(UTC+11:00) 所罗门群岛,新喀里多尼亚"},
        {"en", "(UTC+11:00) Solomon Islands, New Caledonia"},
        {"pt", "(UTC+11:00) Ilhas de Salomão, Nova Caledónia"}
    }}},
    {"Pacific/Auckland", {{
        {"zh", "(UTC+12:00) 奥克兰,惠灵顿"},
        {"en", "(UTC+12:00) Auckland, Wellington"},
        {"pt", "(UTC+12:00) Auckland, Wellington"}
    }}},
    {"Pacific/Tongatapu", {{
        {"zh", "(UTC+13:00) 努库阿洛法"},
        {"en", "(UTC+13:00) Nuku'alofa"},
        {"pt", "(UTC+13:00) Nuku'alofa"}
    }}}
};

static std::string getTranslatedTimezoneName(const std::string& timezoneId, const std::string& defaultName, const std::string& currentLang) {
    auto it = timezoneTranslations.find(timezoneId);
    if (it != timezoneTranslations.end()) {
        auto langIt = it->second.find(currentLang);
        return langIt != it->second.end() ? langIt->second : defaultName;
    }
    return defaultName;
}

bool TrafficControlImpl::getAppResourceDefine(Json::Value& def) {
    TrafficControlPtr ctrlPtr = Object::IFactory::instance<ITrafficControl>("custom.trafficControl");
    if (ctrlPtr!=osNullptr && CustomClient::instance()->hasFunction("ITrafficControl", "getAppResourceDefine")) {
        return ctrlPtr->getAppResourceDefine(def);
    }

    // 多语言版本要增加语言选项
    if (IS_CUSTOM_MODE("multilingual"))
    {
        for (auto iter = langMap.begin(); iter != langMap.end(); ++iter) {
            Json::Value elem = Json::nullValue;
            elem["id"] = iter->first;
            elem["name"] = iter->second;
            def["language"].append(elem);
        }
    }
    else
    {
        // 设置默认语言  mm@ 20250422 
        Json::Value elem = Json::nullValue;
        auto langIt = langMap.find(mDefLang);
        if (langIt != langMap.end()) {
            def["defLanguage"] = mDefLang;
            elem["id"] = langIt->first;
            elem["name"] = langIt->second;
            def["language"].append(elem);
        }
        else {
            def["defLanguage"] = "zh";
            elem["id"] = "zh";
            elem["name"] = "简体中文";
            def["language"].append(elem);
            infof("mm@ mDefLang not found, use zh\n");
        }
    }

    for (const auto& timezoneId : orderedTimezones) {
        // 检查时区ID是否存在于映射中
        auto it = timezoneTranslations.find(timezoneId);
        if (it != timezoneTranslations.end()) {
            Json::Value elem = Json::nullValue;
            elem["id"] = timezoneId;
            elem["name"] = getTranslatedTimezoneName(timezoneId, timezoneId, mLang);
            def["timezone"].append(elem);
        }
    }

    // 设置默认时区
    if (mDefTimezone != "")
    {
        auto timezoneIt = timezoneTranslations.find(mDefTimezone);
        if (timezoneIt != timezoneTranslations.end()) {
            def["defTimezone"] = mDefTimezone;
        }
    }
    else{
        def["defTimezone"] = "Asia/Shanghai";
    }

    infof("custom app resouce define:%s\n", def.toStyledString().c_str());
    return true;
}

bool TrafficControlImpl::getAppSystemIODefine(Json::Value& def) {
    TrafficControlPtr ctrlPtr = Object::IFactory::instance<ITrafficControl>("custom.trafficControl");
    if (ctrlPtr!=osNullptr && CustomClient::instance()->hasFunction("ITrafficControl", "getAppSystemIODefine")) {
        return ctrlPtr->getAppSystemIODefine(def);
    }

    return true;
}

bool TrafficControlImpl::getSystemPeriParams(const char* module,Json::Value& params) {
    TrafficControlPtr ctrlPtr = Object::IFactory::instance<ITrafficControl>("custom.trafficControl");
    if (ctrlPtr!=osNullptr && CustomClient::instance()->hasFunction("ITrafficControl", "getSystemPeriParams")) {
        return ctrlPtr->getSystemPeriParams(module, params);
    }
/*业务有需要可以自自定外设的支持内容    using namespace System;
    if (std::string(module) == "systemCard") {
        params["type"] = ISystemCard::aCard | ISystemCard::bCard;
    }*/

    return true;
}

bool TrafficControlImpl::checkUpgradeParams(bool app,const Json::Value& info,const Json::Value& mainfest) {
    TrafficControlPtr ctrlPtr = Object::IFactory::instance<ITrafficControl>("custom.trafficControl");
    if (ctrlPtr!=osNullptr && CustomClient::instance()->hasFunction("ITrafficControl", "checkUpgradeParams")) {
        return ctrlPtr->checkUpgradeParams(app, info, mainfest);
    }

    /**TODO mainfest["productVersion"] 正式版本一般为 GD-VX.XXXX，测试版本为 TGD-VX.XXXX，行业线根据自身当前版本内容进行版本校验，决定当前是否可以升级*/
    if (mainfest.isMember("productVersion")) {
        const std::string& check = mainfest["productVersion"].asString();
        if (check.find("OS") != std::string::npos) {
            errorf("OS can't upgrade\n");
            return false;
        }
    }
    return true;
}

bool TrafficControlImpl::getSystemCardNo(const char *src, std::string &dst) {

    if (strlen(src) != 8) {
        return true;
    }

    uint32_t cardNo = 0;
    cardNo = std::stoul(src, osNullptr, 16);
    const Json::Value &config = AppConfig::instance()->getAppConfig("device_config");

    if(config.isMember("isIDCardPositive") && config["isIDCardPositive"].asInt() == 2) {
        uint32_t original = cardNo;
        uint32_t reversed = 0;
        reversed |= (original & 0x000000FF) << 24;
        reversed |= ((original & 0x0000FF00) << 8);
        reversed |= ((original & 0x00FF0000) >> 8);
        reversed |= ((original & 0xFF000000) >> 24);
        cardNo = reversed;
    }
    /**卡号为4字节(即8个字符)时要以数值的方式展示，兼容以前*/
    char card[64] = {0};
    sprintf(card, "%010u", cardNo);
    dst = card;
    return true;
}

bool TrafficControlImpl::getWGInputCardNo(const char *src, std::string &dst, const Json::Value &attr) {

    /**wg26/34是10进制 wg50/66是16进制*/
    int32_t type = attr.isMember("wgType")? attr["wgType"].asInt():-1;
    switch (type) {
        case System::IWiegandCtrl::wg50:
        case System::IWiegandCtrl::wg66:{
            char card[128] = {0};
            sprintf(card, "%lx", std::stoul(src));
            dst = card;
            return true;
        }
        default:
            return true;
    }
}

bool TrafficControlImpl::checkIdCardCap() {
    const Json::Value& identifyCaps = System::IDevCapacity::instance()->getFunctionCaps("identify");
    int32_t size = identifyCaps.empty()? 0:identifyCaps.size();
    for (int32_t index = 0; index < size; ++index) {
        std::string elemCaps = identifyCaps[index].asString();
        if (elemCaps == ID_CARD) {
            return true;
        }
    }

    return false;
}

bool TrafficControlImpl::getExternalCardNo(const char *src, std::string &dst, const Json::Value &attr) {
    if (attr.isMember("wgType")) {
        //getWGInputCardNo(src,dst,attr);   按产品需求调整,不对wg50/wg66进行转换
    } else if (attr.isMember("type") && attr["type"].asString() == "GuoMiCard") {
        getGuomiCardNo(src, dst, attr);
    }
    return true;
}

bool TrafficControlImpl::getGuomiCardNo(const char *src, std::string &dst, const Json::Value &attr) {
    uint32_t cardNo = 0;
    if (strlen(src) == 0) {//兼容北京朗远国密卡，此卡上报的是0x00,0x0,0x0,0x0,0x12,0x34,0x56,0x78，需要过滤掉前的0
        int32_t i = 0;
        while (src[i] == 0 && i < 8) {
            i++;
        }
        if (i < 8 && i > 0) {
            cardNo = *(uint32_t*)(&src[i]);
        }
    } else if (strlen(src) <= 4) {//给应用的是4字节的16进制卡号
        cardNo = *(uint32_t*)src;
    } else if (strlen(src) <= 8) {//给应用的是 16进制卡号对应的字符串
        return false;
    }

    const Json::Value &config = AppConfig::instance()->getAppConfig("device_config");
    if(config.isMember("isIDCardPositive") && config["isIDCardPositive"].asInt() == 2) {
        uint32_t original = cardNo;
        uint32_t reversed = 0;
        reversed |= (original & 0x000000FF) << 24;
        reversed |= ((original & 0x0000FF00) << 8);
        reversed |= ((original & 0x00FF0000) >> 8);
        reversed |= ((original & 0xFF000000) >> 24);
        cardNo = reversed;
    }
    /**卡号为4字节(即8个字符)时要以数值的方式展示，兼容以前*/
    char card[64] = {0};
    sprintf(card, "%010u", cardNo);
    dst = card;
    return true;
}


}
}